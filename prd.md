
### **产品需求文档 (PRD)：智能合同管理系统**

**1. 引言**

**1.1. 项目概述**

本项目旨在开发一个智能合同管理Web应用。该应用将简化合同创建、管理和归档的流程，特别是针对需要频繁处理大量相似合同的企业用户。核心功能是利用大型语言模型（如Gemini）自动识别和填充合同模板中的变量，并通过对话式交互快速生成新合同，从而显著提升合同处理效率和准确性。

**1.2. 目标用户**

*   **销售人员/业务经理**：需要为不同客户快速生成销售合同。
*   **采购专员**：需要管理与供应商的采购合同。
*   **法务/行政人员**：负责合同模板的制定、审核和归档。

**2. 功能需求**

**2.1. 合同管理**

*   **2.1.1. 合同模板上传与管理**
    *   **描述**：用户可以上传标准的合同文档（如.docx格式）作为模板。系统应保存这些模板以备将来使用。
    *   **用户故事**：作为一名法务人员，我希望能够上传公司标准的采购合同模板，以便销售团队在需要时使用。
    *   **验收标准**：
        *   支持 .docx 格式的文件上传。
        *   提供一个模板库界面，展示所有已上传的模板列表。
        *   用户可以对模板进行删除或更新。

*   **2.1.2. 合同分类**
    *   **描述**：用户可以为上传的合同模板进行分类，例如：采购合同、销售合同、进口业务合同等。
    *   **用户故事**：作为一名管理员，我希望能将合同模板按“销售”、“采购”等类别进行组织，方便快速查找。
    *   **验收标准**：
        *   允许用户创建、编辑和删除合同分类。
        *   在上传模板时，可以为模板指定一个或多个分类。
        *   在模板库中，支持按分类筛选和查看合同模板。

**2.2. 智能合同变量识别 (集成 Gemini API)**

*   **描述**：当用户上传一个新模板时，系统应调用 Gemini API 来自动识别和提取合同中的关键变量。
*   **用户故事**：作为一名法务人员，当我上传一份销售合同时，我希望系统能自动识别出像【买方名称】、【合同编号】、【货物名称】、【总金额】这样的变量，无需我手动标记。
*   **验收标准**：
    *   在模板上传成功后，系统后端自动向 Gemini API 发送请求，识别合同文本中的潜在变量。
    *   可识别的变量类型应包括但不限于：
        *   **甲方/买方信息** (公司名称, 地址, 联系人)
        *   **乙方/卖方信息** (公司名称, 地址, 联系人)
        *   **合同基本信息** (合同编号, 签订日期)
        *   **货物/服务信息** (品名, 规格, 数量, 单价, 总价)
        *   **关键日期** (交付日期, 付款日期)
        *   **金额与支付条款** (定金比例, 尾款比例)
    *   系统应提供一个界面，让用户可以审核、修改或补充 Gemini 识别出的变量，并将其正式保存为模板的变量字段。

**2.3. 新合同生成**

*   **2.3.1. 基于表单的合同生成**
    *   **描述**：用户选择一个模板后，系统会动态生成一个包含所有已定义变量的输入表单。用户填写完表单信息后，系统将这些信息填充到模板中，生成一份新的合同。
    *   **用户故事**：作为一名销售，我希望能选择“销售合同”模板，然后在一个清晰的表单里填写客户名称、购买的商品和价格，最后点击一个按钮就生成一份完整的合同。
    *   **验收标准**：
        *   用户选择模板后，前端自动渲染出对应变量的输入字段。
        *   用户填写完信息后，点击“生成合同”按钮。
        *   系统将填写的信息替换掉模板中的变量占位符，生成一份完整的合同。

*   **2.3.2. 合同预览与下载**
    *   **描述**：生成的新合同支持在线预览，并可以下载为 PDF 格式。
    *   **验收标准**：
        *   生成合同后，页面上会有一个预览区域，清晰地展示合同内容。
        *   提供“下载PDF”按钮，用户点击后可以下载最终生成的合同文件。
        *   PDF 文件的格式和内容应与预览完全一致。

**2.4. 对话式合同生成 (核心功能)**

*   **描述**：提供一个聊天机器人界面。用户可以通过自然语言对话的方式，指定需要生成的合同类型和关键信息，机器人会引导用户补充所有必要的变量，最终生成合同。
*   **用户故事**：作为一名业务经理，我希望能直接对系统说：“帮我生成一份销售合同，客户是‘ABC科技公司’”。然后系统会一步步问我需要填写哪些其他信息，比如商品、数量等，我回答完后合同就生成了。
*   **验收标准**：
    *   **启动对话**：用户可以在对话框中输入指令，如：“创建一份采购合同”。
    *   **识别意图和模板**：系统调用 Gemini 理解用户意图，并匹配到【合同管理】中的相应合同模板。
    *   **智能提问与信息补全**：
        *   系统会根据模板中定义的变量，逐一向用户提问以收集信息。例如：“好的，采购合同。请输入‘供应商名称’。”
        *   如果用户在第一句指令中已提供了部分信息（如客户名称），系统应能识别并跳过对应问题。
    *   **信息确认**：在所有信息收集完毕后，系统会向用户总结并确认所有变量信息。
    *   **生成合同**：用户确认无误后，系统调用【2.3. 新合同生成】的功能，生成合同并提供预览和下载链接。

**3. 非功能性需求**

**3.1. 技术栈**

*   **前端**：建议使用 Next.js 或类似的 React 框架，便于在 Vercel 上部署。
*   **后端**：可使用 Next.js 的 API Routes 或一个独立的 Node.js 服务 (如 Express)。
*   **数据库**：使用 **Supabase** 作为后端服务和数据库。利用其认证 (Authentication) 和存储 (Storage) 功能。
*   **代码管理**：所有代码通过 **GitHub**进行版本控制。
*   **部署**：应用最终部署在 **Vercel** 平台。

**3.2. 易用性与界面风格**

*   **设计理念**：整体风格追求简洁、直观、易于上手。减少不必要的点击和复杂的界面层级。
*   **交互**：对话式生成功能应提供流畅、自然的交互体验，反馈及时明确。
*   **响应式设计**：应用界面应能良好地适配桌面浏览器。

**3.3. 性能**

*   **响应时间**：页面加载和合同生成过程应尽可能快，避免用户长时间等待。
*   **API调用**：对 Gemini API 的调用需要进行优化，管理好配额和成本。

**3.4. 安全性**

*   **数据隔离**：确保不同租户或用户之间的合同数据是严格隔离的。
*   **认证**：用户需要登录后才能访问自己的合同和模板，可使用 Supabase Auth 实现。

**4. 附录：参考合同**

*   本文档的附件（您提供的销售合同图片）将作为“销售合同”和“进口业务合同”的初始参考模板。在开发过程中，应以此为依据来设计变量提取和模板填充功能。

---



这个 PRD 为您的项目提供了一个清晰的路线图。祝您开发顺利！