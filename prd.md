

## **产品需求文档 (PRD): 飞书合同内容更新助手**

### 1. **产品概述**

#### 1.1 **产品名称**
飞书合同内容更新助手

#### 1.2 **产品简介**
“飞书合同内容更新助手”是一款 Web 应用，旨在简化和自动化更新飞书云文档（特别是合同范本）中的特定内容。用户可以通过上传在线飞书文档链接，然后使用自定义指令或上传截图，快速替换文档中的甲方、乙方、合同金额等关键信息。该应用将通过飞书云文档的 MCP (Model Context Protocol) 进行集成，并部署在 Vercel 平台上。

#### 1.3 **目标用户**
需要频繁处理和修改标准化文档（如合同、协议）的法务人员、销售人员、项目经理及行政人员。

#### 1.4 **核心价值**
*   **提升效率**：自动化替换文档中的重复性信息，减少手动修改的时间和精力。
*   **降低错误率**：程序化处理确保了信息替换的准确性，避免人工操作可能出现的疏漏。
*   **简化流程**：提供直观的 Web 界面，用户无需复杂操作即可完成文档更新。

### 2. **产品功能需求**

#### 2.1 **用户认证与授权**

*   **需求描述**: 用户需要能够授权应用访问其飞书云文档。
*   **实现细节**:
    *   通过飞书开放平台的 OAuth 2.0 协议进行用户身份认证。
    *   应用需要向用户申请读取和写入飞书云文档的权限。
    *   用户的授权信息（如 `access_token`）需要被安全地存储和管理。

#### 2.2 **飞书文档链接输入**

*   **需求描述**: 用户可以在 Web 应用界面中输入一个在线的飞书云文档链接。
*   **实现细节**:
    *   提供一个输入框，用于粘贴飞书云文档的 URL。
    *   后端需要能够解析此 URL，提取出文档的唯一标识符（`document_id`）。
    *   应用需要验证该文档的有效性以及当前用户是否拥有读写权限。

#### 2.3 **内容更新方式**

用户可以通过以下两种方式指定需要更新的内容：

**2.3.1 通过自定义指令更新**

*   **需求描述**: 用户可以通过简单的键值对指令，指定需要替换的旧文本和新内容。
*   **实现细节**:
    *   **前端界面**: 提供一个动态表单，用户可以添加多个“查找内容”和“替换为”的输入组。例如：
        *   查找内容：“甲方” -> 替换为：“[新的甲方公司名]”
        *   查找内容：“乙方” -> 替换为：“[新的乙方公司名]”
    *   **后端逻辑**:
        *   接收前端传递的替换规则列表。
        *   调用飞书云文档 API，首先获取文档的所有内容块 (blocks)。
        *   遍历所有内容块，查找并替换指定的文本。
        *   调用飞书 API 的 `update_feishu_block_text` 或 `batch_create_feishu_blocks` 等接口，将修改后的内容写回文档。

**2.3.2 通过上传截图更新**

*   **需求描述**: 用户可以上传包含新信息的截图（例如，包含新的甲方、乙方信息的表单截图），应用自动识别并更新到文档中。
*   **实现细节**:
    *   **前端界面**: 提供一个文件上传区域，允许用户拖拽或选择图片文件（支持 PNG, JPEG 等常见格式）。
    *   **后端逻辑**:
        *   接收上传的图片文件。
        *   集成 OCR (Optical Character Recognition) 服务或库，从图片中提取文本信息。 例如，可以识别出“甲方：XXX公司”、“乙方：YYY公司”等字段。
        *   对识别出的文本进行解析，提取出需要更新的键值对。可以预设一些常见的关键词（如“甲方”、“乙方”、“合同编号”等）来辅助提取。
        *   调用飞书云文档 API，执行与自定义指令更新类似的查找和替换操作。

#### 2.4 **处理结果反馈**

*   **需求描述**: 在内容更新操作完成后，应向用户提供明确的结果反馈。
*   **实现细节**:
    *   **成功状态**: 提示用户文档已成功更新，并可提供一个直接返回该飞书文档的链接。
    *   **失败状态**: 如果更新过程中出现任何错误（如权限不足、文档不存在、未找到指定内容等），应向用户显示清晰的错误提示信息。

### 3. **技术架构与部署**

#### 3.1 **飞书云文档集成**

*   **核心技术**: 飞书云文档 MCP (Model Context Protocol)。
*   **实现细节**:
    *   需要创建一个企业自建应用，并获取 App ID 和 App Secret。
    *   在飞书开放平台为应用开启云文档的读取和写入权限。
    *   后端服务将实现一个 MCP 服务器，用于处理与飞书 API 的交互。 这包括获取和刷新访问令牌、调用文档相关的 API 等。

#### 3.2 **Web 应用**

*   **前端框架**: 推荐使用 Next.js 或 React，这与 Vercel 平台有很好的集成。
*   **后端逻辑**: 可以使用 Vercel 的 Serverless Functions 来处理 API 请求和业务逻辑。

#### 3.3 **部署**

*   **平台**: Vercel。
*   **流程**:
    *   将项目代码托管在 GitHub、GitLab 或 Bitbucket 上。
    *   将代码仓库与 Vercel 项目关联，实现 CI/CD (持续集成/持续部署)。
    *   Vercel 会自动为每次代码提交生成预览部署。

#### 3.4 **数据库与存储**

*   **数据库**:
    *   推荐使用 Vercel Postgres 或 Vercel KV 等 Vercel 提供的存储解决方案来存储用户授权信息、操作日志等。
    *   PlanetScale 等免费的 MySQL 云数据库也是一个可行的选择。
*   **文件存储**:
    *   对于用户上传的截图，可以使用 Vercel Blob 进行存储和管理。 Vercel 对 Serverless Functions 的请求体大小有限制（通常为4.5MB），因此推荐采用客户端直传到 Blob 存储的方式。

### 4. **非功能性需求**

*   **性能**: 接口响应时间应在 2 秒以内。对于 OCR 处理等耗时操作，应提供异步处理和进度提示。
*   **安全性**:
    *   所有敏感信息（如飞书 App Secret, 数据库密码）必须通过环境变量进行管理，不得硬编码在代码中。
    *   用户授权的 `access_token` 必须加密存储。
*   **用户体验**: 界面设计应简洁明了，操作流程直观易懂。

### 5. **开发路线图 (Roadmap)**

*   **第一阶段 (MVP - 最小可行产品)**
    1.  完成飞书 OAuth 认证和授权流程。
    2.  实现通过输入飞书文档链接加载文档。
    3.  实现基于自定义指令的文本内容查找和替换功能。
    4.  完成在 Vercel 平台的基础部署。
*   **第二阶段**
    1.  集成 Vercel Blob，实现截图上传功能。
    2.  集成 OCR 服务，实现从截图中提取文本并更新文档的功能。
    3.  完善前端界面和用户体验，增加操作反馈和错误处理。
*   **第三阶段**
    1.  引入数据库，存储用户信息和操作历史。
    2.  优化 OCR 识别的准确率，支持更复杂的版面解析。
    3.  探索更智能的“自研语言”，例如支持自然语言指令（“把合同里的甲方改成XX公司”）。

